import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams, Res } from 'routing-controllers';
import { AddEmailRequest, EmailResponse, GetPagingRequest, Page, UpdateEmailRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { AuthorizedUser } from '../component/http';
import { User } from '../entity/user';
import { isPagination } from '../component/util';
import { Mail } from '../entity/email';
import { createGmailService, getGmailAuthUrl, GmailService } from '../component/service/gMail';
import { createEmailProcessorService } from '../component/service/emailProcessor';
import { Project } from '../entity/project';

function getResponse(entry: Mail): EmailResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    type: entry.type,
    sender: entry.sender,
    email: entry.email,
    authorized: entry.authorized,
    activated: entry.activated,
    companyName: entry.companyName,
    companyAddress: entry.companyAddress,
    productPage: entry.productPage,
    contactUs: entry.contactUs,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class EmailController {
    private readonly emailRepository = getRepository(Mail);
    private readonly userRepository = getRepository(User);
    private readonly projectRepository = getRepository(Project);

    @Get(`/v1/emails`)
    @Authorized()
    public async index(@QueryParams() query: { projectId?: string; } & GetPagingRequest): Promise<EmailResponse[] | Page<EmailResponse>> {
      const [emails, total] = await this.emailRepository.findAndCount({
        where: {
          projectId: query.projectId,
        },
        order: {
          createTime: 'DESC',
        },
      });
      const items = emails.map(getResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Get(`/v1/emails/checkAuthorized`)
    @Authorized()
    public async checkAuthorized(@QueryParams() query: { email: string; }): Promise<string | boolean> {
      console.log('checkAuthorized: ', query.email);

      try {
        const email = await this.emailRepository.findOneBy({ email: query.email });

        if (!email) {
          return getGmailAuthUrl();
        }

        if (email.accessToken && email.expiresAt > new Date()) {
          return true;
        } else {
          return getGmailAuthUrl();
        }

      } catch (error) {
        console.warn(`检查邮箱 ${query.email} 授权状态时出错:`, error);
        return getGmailAuthUrl();
      }
    }

    @Post(`/v1/emails`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddEmailRequest): Promise<EmailResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });

      const email = await this.emailRepository.save(
        this.emailRepository.create({
          user: user,
          projectId: request.projectId,
          type: request.type,
          sender: request.sender,
          email: request.email,
          authorized: request.authorized,
          companyName: request.companyName,
          companyAddress: request.companyAddress,
          productPage: request.productPage,
          contactUs: request.contactUs,
          activated: true,
        })
      );
      await this.emailRepository.save(email);

      return getResponse(email);
    }

    @Patch(`/v1/emails/:mailId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@Body() request: UpdateEmailRequest, @Param('mailId') mailId: string ): Promise<EmailResponse> {
      const email = await this.emailRepository.findOneByOrFail({ id: mailId });
      if (request.activated !== undefined) { email.activated = request.activated; }
      if (request.companyName !== undefined) { email.companyName = request.companyName; }
      if (request.companyAddress !== undefined) { email.companyAddress = request.companyAddress; }
      if (request.productPage !== undefined) { email.productPage = request.productPage; }
      if (request.contactUs !== undefined) { email.contactUs = request.contactUs; }

      return getResponse(await this.emailRepository.save(email));
    }

    @Delete(`/v1/emails/:mailId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('mailId') mailId: string): Promise<void> {
      const email = await this.emailRepository.findOneByOrFail({
        id: mailId,
      });
      email.deleteTime = new Date();
      await this.emailRepository.save(email);
    }

    @Get(`/v1/auth/gmail/callback`)
    @HttpCode(StatusCodes.OK)
    public async webhook(@QueryParams() query: { code?: string; state?: string; error?: string; email?: string }): Promise<void> {
      console.log('Gmail授权回调:', { code: query.code, state: query.state, error: query.error, email: query.email });

      if (query.error) {
        console.error('Gmail授权被拒绝:', query.error);
        return;
      }

      if (!query.code) {
        console.error('缺少授权码');
        return;
      }

      try {
        // 1. 创建Gmail服务实例并获取令牌
        const gmailService = createGmailService();
        const tokens = await gmailService.getTokenFromCode(query.code);

        console.log('✅ 成功获取Gmail访问令牌');

        // 2. 设置凭证并获取用户邮箱信息
        gmailService.setCredentials(tokens.accessToken, tokens.refreshToken);
        const profile = await gmailService.getUserProfile();
        const userEmail = profile.emailAddress;

        if (!userEmail) {
          throw new Error('无法获取用户邮箱地址');
        }

        console.log(`📧 用户邮箱: ${userEmail}`);

        // 3. 查找或创建邮箱记录
        // const emailRecord = await this.emailRepository.findOne({
        //   where: { email: userEmail },
        //   relations: ['user'],
        // });
        //
        // if (!emailRecord) {
        //   console.warn(`⚠️ 未找到邮箱记录: ${userEmail}，请先在系统中添加此邮箱`);
        //   return;
        // }

        // 4. 更新邮箱记录的OAuth信息
        emailRecord.accessToken = tokens.accessToken;
        emailRecord.refreshToken = tokens.refreshToken;
        emailRecord.authorized = true;

        // 设置令牌过期时间（通常为1小时）
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 1);
        emailRecord.expiresAt = expiresAt;

        await this.emailRepository.save(emailRecord);
        console.log(`✅ 已更新邮箱授权信息: ${userEmail}`);

        // 5. 自动处理未读邮件
        const emailProcessor = createEmailProcessorService();
        const processingResult = await emailProcessor.processUnreadEmails(emailRecord, gmailService, {
          maxEmails: 10,
          retryAttempts: 3,
          retryDelay: 1000,
        });

        if (processingResult.success) {
          console.log(`✅ 邮件处理完成 - 处理了 ${processingResult.processedCount} 封邮件`);
        } else {
          console.warn(`⚠️ 邮件处理部分失败 - 成功: ${processingResult.processedCount}, 失败: ${processingResult.failedCount}`);
          processingResult.errors.forEach(error => console.error(`❌ ${error}`));
        }

      } catch (error) {
        console.error('❌ Gmail授权回调处理失败:', error);
      }
    }

    /**
     * 手动触发邮件处理
     */
    @Post(`/v1/emails/:mailId/process`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async processEmails(@Param('mailId') mailId: string): Promise<{ success: boolean; message: string; result?: any }> {
      try {
        // 1. 获取邮箱记录
        const emailRecord = await this.emailRepository.findOne({
          where: { id: mailId },
          relations: ['user'],
        });

        if (!emailRecord) {
          return {
            success: false,
            message: '邮箱记录不存在',
          };
        }

        // 2. 检查授权状态
        if (!emailRecord.authorized || !emailRecord.accessToken) {
          return {
            success: false,
            message: '邮箱未授权，请先完成Gmail授权',
          };
        }

        // 3. 创建Gmail服务并设置凭证
        const gmailService = createGmailService();
        gmailService.setCredentials(emailRecord.accessToken, emailRecord.refreshToken);

        // 4. 处理未读邮件
        const emailProcessor = createEmailProcessorService();
        const processingResult = await emailProcessor.processUnreadEmails(emailRecord, gmailService, {
          maxEmails: 10,
          retryAttempts: 3,
          retryDelay: 1000,
        });

        return {
          success: processingResult.success,
          message: processingResult.success
            ? `邮件处理完成 - 成功处理 ${processingResult.processedCount} 封邮件`
            : `邮件处理部分失败 - 成功: ${processingResult.processedCount}, 失败: ${processingResult.failedCount}`,
          result: processingResult,
        };

      } catch (error) {
        console.error('❌ 手动邮件处理失败:', error);
        return {
          success: false,
          message: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }
}
