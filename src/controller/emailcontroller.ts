import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams, Res } from 'routing-controllers';
import { AddEmailRequest, EmailResponse, GetPagingRequest, Page, UpdateEmailRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { AuthorizedUser } from '../component/http';
import { User } from '../entity/user';
import { isPagination } from '../component/util';
import { Mail } from '../entity/email';
import { createGmailService, getGmailAuthUrl } from '../component/service/gMail';

function getResponse(entry: Mail): EmailResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    type: entry.type,
    sender: entry.sender,
    email: entry.email,
    authorized: entry.authorized,
    activated: entry.activated,
    companyName: entry.companyName,
    companyAddress: entry.companyAddress,
    productPage: entry.productPage,
    contactUs: entry.contactUs,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class EmailController {
    private readonly emailRepository = getRepository(Mail);
    private readonly userRepository = getRepository(User);

    @Get(`/v1/emails`)
    @Authorized()
    public async index(@QueryParams() query: { projectId?: string; } & GetPagingRequest): Promise<EmailResponse[] | Page<EmailResponse>> {
      const [emails, total] = await this.emailRepository.findAndCount({
        where: {
          projectId: query.projectId,
        },
        order: {
          createTime: 'DESC',
        },
      });
      const items = emails.map(getResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Get(`/v1/emails/checkAuthorized`)
    @Authorized()
    public async checkAuthorized(@QueryParams() query: { email: string; }): Promise<string | boolean> {
      console.log('checkAuthorized: ', query.email);

      try {
        const email = await this.emailRepository.findOneBy({ email: query.email });

        if (!email) {
          return getGmailAuthUrl();
        }

        if (email.accessToken && email.expiresAt > new Date()) {
          return true;
        } else {
          return getGmailAuthUrl();
        }

      } catch (error) {
        console.warn(`检查邮箱 ${query.email} 授权状态时出错:`, error);
        return getGmailAuthUrl();
      }
    }

    @Post(`/v1/emails`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddEmailRequest): Promise<EmailResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });

      const email = await this.emailRepository.save(
        this.emailRepository.create({
          user: user,
          projectId: request.projectId,
          type: request.type,
          sender: request.sender,
          email: request.email,
          authorized: request.authorized,
          companyName: request.companyName,
          companyAddress: request.companyAddress,
          productPage: request.productPage,
          contactUs: request.contactUs,
          activated: true,
        })
      );
      await this.emailRepository.save(email);

      return getResponse(email);
    }

    @Patch(`/v1/emails/:mailId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@Body() request: UpdateEmailRequest, @Param('mailId') mailId: string ): Promise<EmailResponse> {
      const email = await this.emailRepository.findOneByOrFail({ id: mailId });
      if (request.activated !== undefined) { email.activated = request.activated; }
      if (request.companyName !== undefined) { email.companyName = request.companyName; }
      if (request.companyAddress !== undefined) { email.companyAddress = request.companyAddress; }
      if (request.productPage !== undefined) { email.productPage = request.productPage; }
      if (request.contactUs !== undefined) { email.contactUs = request.contactUs; }

      return getResponse(await this.emailRepository.save(email));
    }

    @Delete(`/v1/emails/:mailId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('mailId') mailId: string): Promise<void> {
      const email = await this.emailRepository.findOneByOrFail({
        id: mailId,
      });
      email.deleteTime = new Date();
      await this.emailRepository.save(email);
    }

    @Get(`/v1/auth/gmail/callback`)
    @HttpCode(StatusCodes.OK)
    public async webhook(@QueryParams() query: { code?: string; state?: string; error?: string }): Promise<void> {
      console.log('callback: ', query.code, query.state, query.error);

      if (query.error) {
        console.error('Gmail授权被拒绝:', query.error);
        return;
      }

      if (!query.code) {
        console.error('缺少授权码');
        return;
      }
      
    }
}
