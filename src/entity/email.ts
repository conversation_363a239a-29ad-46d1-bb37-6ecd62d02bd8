import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user';
import { MailType } from '../component/types';

@Entity('email')
export class Mail extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, user => user.id, { orphanedRowAction: 'delete', onDelete: 'CASCADE' })
    user: User;

    @Column('text', { nullable: false })
    projectId: string;

    @Column('boolean', { nullable: true, default: true })
    activated: boolean;

    @Column('text', { nullable: false })
    type: MailType;

    @Column('text', { nullable: false })
    sender: string;

    @Column('text', { nullable: false })
    email: string;

    @Column('boolean', { nullable: true, default: false })
    authorized: boolean;

    @Column('text', { nullable: true })
    accessToken: string;

    @CreateDateColumn()
    expiresAt: Date;

    @Column('text', { nullable: true })
    companyName: string;

    @Column('text', { nullable: true })
    companyAddress: string;

    @Column('text', { nullable: true })
    productPage: string;

    @Column('text', { nullable: true })
    contactUs: string;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false })
    updateTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
