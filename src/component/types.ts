import { IsBoolean, IsISO8601, IsNotEmpty, IsNumberString, IsObject, IsOptional, IsString } from 'class-validator';

export interface Page<T> {
  total: number;
  items: T[];
}

export class LoginRequest {
  @IsString()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class SSOLoginRequest {
  @IsString()
  @IsNotEmpty()
  code: string;

  @IsString()
  @IsOptional()
  state?: string;
}

export interface TokenResponse {
  token: string;
}

export class GetUsersQuery {
  @IsString()
  @IsOptional()
  username?: string;

  @IsString()
  @IsOptional()
  ids?: string;

  @IsString()
  @IsOptional()
  roles?: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export class RegisterRequest {
  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString({ each: true })
  @IsOptional()
  roles: string[];
}

export class UpdateUserRequest {
  @IsString()
  @IsOptional()
  oldPassword?: string;

  @IsString()
  @IsOptional()
  password?: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  username?: string;

  @IsString({ each: true })
  @IsOptional()
  roles?: string[];
}

export class ResetUserRequest {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString({ each: true })
  @IsOptional()
  roles?: string[];
}

export interface ProjectResponse {
  id: string;
  name: string;
  website: string;
  knowledgeId: string;
  settings: Settings;
  createTime: string;
}

export interface ChatResponse {
  sessionId: string;
  question: string;
  answer: string;
  reference: unknown;
  like: number;
  createTime: string;
}

export enum ResponseCode {
  username_not_exist = '10000', //用户名不存在
  username_exist = '10001', //用户名已存在
  invalid_role = '10002', //无效的角色
  invalid_user = '10003', //无效的用户
  invalid_old_password = '10004', //无效的旧密码
  cannot_delete_self = '10005',
  invalid_username_or_password = '10006', //无效的用户名或密码
  invalid_email = '10007', //无效的邮箱地址
  upload_knowledge_failed = '10012', //文档上传知识库失败
  knowledge_exist = '10015', //知识库已存在
  create_knowledge_failed = '10016',  //创建知识库失败
  delete_knowledge_failed = '10017', //删除知识库失败
  update_knowledge_failed = '10018', //更新知识库失败
  delete_knowledge_file_failed = '10019', //删除知识库失败
  create_chat_agent_failed = '10020', //创建对话失败
  create_session_failed = '10021', //创建会话失败
  create_chat_failed = '10022', //创建对话失败
}

export interface UserResponse {
  id: string;
  username: string;
  roles: string[];
  createTime: Date;
  email: string;
  lastLoginTime: Date | null;
}


export class AddWebsiteRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public websiteUrl: string;
}

export class AddFileRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsOptional()
  public folderId?: string;
}

export interface FileResponse {
  id: string;
  projectId: string;
  name: string;
  path: string;
  type: KnowledgeType;
  folder?: FolderResponse;
  mimeType: string;
  size: number;
  status: FileParseStatus;
  createTime: string;
}

export class CreateFolderRequest {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsString()
  @IsOptional()
  parentId?: string;
}

export interface FolderResponse {
  id: string;
  projectId: string;
  name: string;
  parent?: FolderResponse;
  folders: FolderResponse[];
  createTime: Date;
}

export class GetFoldersQuery {
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsString()
  @IsOptional()
  parentId?: string;
}

export class GetFilesQuery {
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsString()
  @IsOptional()
  folderId?: string;

  @IsString()
  @IsOptional()
  type?: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export interface FileTypeStatistics {
  type: KnowledgeType;
  count: number;
}

export class UpdateFolderRequest {
  @IsString()
  @IsOptional()
  parentId?: string;

  @IsString()
  @IsOptional()
  name?: string;
}

export class UpdateFileRequest {
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class GetKnowledgeQuery {
  @IsString()
  @IsOptional()
  public name?: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export class GetDashboardQuery {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsISO8601()
  @IsOptional()
  public startTime?: string;

  @IsISO8601()
  @IsOptional()
  public endTime?: string;
}

export class GetSessionsQuery {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;

  @IsISO8601()
  @IsOptional()
  public startTime?: string;

  @IsISO8601()
  @IsOptional()
  public endTime?: string;
}

export class AddSessionRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public name: string;

  @IsObject()
  @IsOptional()
  public details?: SessionDetails;

  @IsObject()
  @IsOptional()
  public customerInfo?: CustomerInfo;
}

export class GetPagingRequest {
  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export class AddFormRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public name: string;

  @IsString()
  @IsOptional()
  public description?: string;

  @IsString()
  @IsOptional()
  public logo?: string;

  @IsString()
  @IsOptional()
  public content?: string;

  @IsBoolean()
  @IsOptional()
  public active?: boolean;
}

export class UpdateFormRequest {
  @IsString()
  @IsOptional()
  public name?: string;

  @IsString()
  @IsOptional()
  public description?: string;

  @IsString()
  @IsOptional()
  public logo?: string;

  @IsString()
  @IsOptional()
  public content?: string;

  @IsBoolean()
  @IsOptional()
  public active?: boolean;
}

export class AddSubmissionRequest {
  @IsString()
  @IsNotEmpty()
  public formId: string;

  @IsString()
  @IsNotEmpty()
  public content: string;
}

export class AddPipelineRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public pipeline: string;
}

export class UpdatePipelineRequest {
  @IsString()
  @IsNotEmpty()
  public pipeline: string;
}

export interface SessionResponse {
  id: string;
  projectId: string;
  name: string;
  details: SessionDetails;
  customerInfo: CustomerInfo;
  summary: string;
  formFilled: boolean;
  createTime: string;
}

export interface ChatStatistics {
  totalConversations: number;
  totalChats: number;
  totalCountry: number;
  totalCity: number;
}

export interface DashboardResponse {
  overview: {
    totalConversations: number;
    totalKnowledge: number;
    totalWebsites: number;
    totalDocuments: number;
    conversations: {
      date: string;
      count: number;
    }[]
  },
  latestConversations: {
    id: string;
    name: string;
    createTime: string;
  }[],
  topLocations: {
    country: string;
    count: number;
  }[],
  topChannels: {
    channel: string;
    count: number;
  }[]
}

export class GetChatsRequest {
  @IsString()
  @IsNotEmpty()
  public sessionId: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export class ChatRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public question: string;

  @IsString()
  @IsOptional()
  public sessionId?: string;
}

export class AddProjectRequest {
  @IsString()
  @IsOptional()
  projectId?: string;

  @IsString()
  @IsNotEmpty()
  public name: string;

  @IsString()
  @IsNotEmpty()
  public website: string;

  @IsObject()
  @IsOptional()
  public settings?: Settings;
}

export class UpdateProjectRequest {
  @IsString()
  @IsOptional()
  public name?: string;

  @IsObject()
  @IsOptional()
  public settings?: Settings;
}

export interface KnowledgeApiResponse {
  code: number;
  data: {
    id: string;
    name: string;
    description: string | null;
    create_date: string;
    create_time: number;
  };
}

export interface ProductResponse {
  id: string;
  name: string;
  type: ProductType;
  description: string;
  responses: number;
  bots: number;
  teamMembers: number;
  branding: boolean;
  documents: number;
  popular: number;
  price: number;
  paymentLink: string;
  interval: IntervalType;
  additionalInfo: string;
  createTime: string;
}

export interface FormResponse {
  id: string;
  projectId: string;
  name: string;
  description: string;
  logo: string;
  content: string;
  url: string;
  active: boolean;
  createTime: string;
  updateTime: string;
}

export class AddEmailRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  type: MailType;

  @IsString()
  @IsNotEmpty()
  sender: string;

  @IsString()
  @IsNotEmpty()
  email: string;

  @IsBoolean()
  @IsNotEmpty()
  authorized: boolean;

  @IsString()
  @IsOptional()
  companyName?: string;

  @IsString()
  @IsOptional()
  companyAddress: string;

  @IsString()
  @IsOptional()
  productPage: string;

  @IsString()
  @IsOptional()
  contactUs: string;
}

export class UpdateEmailRequest {
  @IsBoolean()
  @IsOptional()
  activated?: boolean;

  @IsString()
  @IsOptional()
  companyName?: string;

  @IsString()
  @IsOptional()
  companyAddress: string;

  @IsString()
  @IsOptional()
  productPage: string;

  @IsString()
  @IsOptional()
  contactUs: string;
}

export interface EmailResponse {
  id: string;
  projectId: string;
  type: MailType;
  sender: string;
  email: string;
  authorized: boolean;
  activated: boolean;
  companyName: string;
  companyAddress: string;
  productPage: string;
  contactUs: string;
  createTime: string;
}

export interface TemplateResponse {
  id: string;
  name: string;
  description: string;
  logo: string;
  content: string;
  createTime: string;
}

export interface SubmissionResponse {
  id: string;
  formId: string;
  content: string;
  createTime: string;
}

export interface PipelineResponse {
  id: string;
  projectId: string;
  pipeline: string;
  createTime: string;
  updateTime: string;
}

export interface PipelineTemplateResponse {
  id: string;
  name: string;
  description: string;
  content: string;
  createTime: string;
}

export interface FileUploadResponse {
  code: number;
  data: [{
    chunk_method: string;
    id: string;
    name: string;
    location: string;
    run: string;
    size: number;
    type: string;
  }];
}

export interface Settings {
  logo?: string;
  name: string;
  color?: string;
  welcomeMsg?: string;
  suggestedEnable?: boolean;
  suggestedQuestions?: [];
  formEnable?: boolean;
  externalFormUrl?: string;
  internalFormUrl?: string;
  currentForm?: number;   // 0: internal form, 1: external form
  whatsappEnable?: boolean;
  whatsappAddress?: string;
  notificationEnabled?: boolean;
  notificationEmails?: [];
  customPrompt?: string;
}

export interface Limits {
  responses: number;
  bots: number;
  teamMembers: number;
  branding: boolean;
  documents: number;
}

export interface Variants {
  variants: {
    price: number;
    interval: string;
  }[]
}

export interface SessionDetails {
  status: SessionStatus;
  channel: ChannelType;
}

export interface CustomerInfo {
  country: string;
  city: string;
  browser: string;
  system: string;
}

export enum FileParseStatus {
  Parsing = 'Parsing',
  Parsed = 'Parsed',
  Failed = 'Failed',
}

export enum KnowledgeType {
  FAQ = 'FAQ',
  Website = 'Website',
  Document = 'Document',
}

export enum SessionStatus {
  Open = 'Open',
  Closed = 'Closed',
}

export enum ChannelType {
  WebChat = 'WebChat',
  WhatsApp = 'WhatsApp',
}

export enum ProductType {
  Free = 'Free',
  Starter = 'Starter',
  Pro = 'Pro',
  Enterprise = 'Enterprise',
}

export enum IntervalType {
  Monthly = 'Monthly',
  Yearly = 'Yearly',
}

export enum MailType {
  Gmail = 'Gmail',
  Outlook = 'Outlook/Hotmail',
}

export enum ChunkMethod {
  naive = 'naive',
  manual = 'manual',
  qa = 'qa',
  table = 'table',
  paper = 'paper',
  book = 'book',
  laws = 'laws',
  presentation = 'presentation',
  picture = 'picture',
  one = 'one',
  email = 'email'
}

export interface CurrentProductResponse {
  productId: string;
  productName: string;
  expireTime: string;
}

// FAQ related types
export class AddFaqRequest {
  @IsString()
  @IsNotEmpty()
  public projectId: string;

  @IsString()
  @IsNotEmpty()
  public question: string;

  @IsString()
  @IsNotEmpty()
  public answer: string;
}

export class UpdateFaqRequest {
  @IsString()
  @IsOptional()
  public question?: string;

  @IsString()
  @IsOptional()
  public answer?: string;
}

export class GetFaqsQuery {
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @IsString()
  @IsOptional()
  keyword?: string;

  @IsNumberString()
  @IsOptional()
  public pageNumber?: string;

  @IsNumberString()
  @IsOptional()
  public pageSize?: string;
}

export interface FaqResponse {
  id: string;
  projectId: string;
  question: string;
  answer: string;
  createTime: string;
  updateTime: string;
}
