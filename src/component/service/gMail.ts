import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { gmail_v1 } from 'googleapis';
import { config } from '../config';

export interface EmailMessage {
    id: string;
    threadId: string;
    subject: string;
    from: string;
    to: string;
    date: string;
    body: string;
    snippet: string;
    isUnread: boolean;
}

export interface SendEmailOptions {
    to: string;
    subject: string;
    text?: string;
    html?: string;
    replyToMessageId?: string;
    threadId?: string;
}

export interface AttachmentInfo {
    attachmentId: string;
    filename: string;
    mimeType: string;
    size: number;
}

// Gmail 工具类
export class GmailService {
    private oauth2Client: OAuth2Client;
    private gmail: gmail_v1.Gmail;
    private isAuthenticated = false;
    private refreshToken: string;
    private accessToken: string;

    constructor() {
      this.oauth2Client = new google.auth.OAuth2(
        config.SSO.google.client_id,
        config.SSO.google.client_secret,
        config.SSO.google.gmail_redirect_uri,
      );

      // 设置凭证
      if (this.refreshToken || this.accessToken) {
        this.oauth2Client.setCredentials({
          refresh_token: this.refreshToken,
          access_token: this.accessToken,
        });
      }

      // 初始化 Gmail API 客户端
      this.gmail = google.gmail({ version: 'v1', auth: this.oauth2Client });
    }

    public ensureAuthenticated(): void {
      if (!this.isAuthenticated) {
        throw new Error('未完成认证，请先调用 getTokenFromCode 或 setCredentials 方法');
      }
    }

    /**
     * 获取授权 URL，用于用户授权
     */
    getAuthUrl(scopes: string[] = ['https://www.googleapis.com/auth/gmail.readonly', 'https://www.googleapis.com/auth/gmail.send']): string {
      return this.oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: scopes,
        prompt: 'consent',
      });
    }

    /**
     * 通过授权码获取令牌
     */
    async getTokenFromCode(code: string): Promise<{ accessToken: string; refreshToken: string }> {
      try {
        const { tokens } = await this.oauth2Client.getToken(code);
        this.oauth2Client.setCredentials(tokens);

        return {
          accessToken: tokens.access_token || '',
          refreshToken: tokens.refresh_token || '',
        };
      } catch (error) {
        throw new Error(`获取令牌失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 刷新访问令牌
     */
    async refreshAccessToken(): Promise<string> {
      try {
        this.ensureAuthenticated();
        const { credentials } = await this.oauth2Client.refreshAccessToken();
        this.oauth2Client.setCredentials(credentials);
        return credentials.access_token || '';
      } catch (error) {
        throw new Error(`刷新令牌失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 获取用户邮箱信息
     */
    async getUserProfile(): Promise<gmail_v1.Schema$Profile> {
      try {
        this.ensureAuthenticated();
        const response = await this.gmail.users.getProfile({ userId: 'me' });
        return response.data;
      } catch (error) {
        throw new Error(`获取用户信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 获取未读邮件列表
     */
    async getUnreadEmails(maxResults = 10): Promise<EmailMessage[]> {
      try {
        this.ensureAuthenticated();
        // 搜索未读邮件
        const response = await this.gmail.users.messages.list({
          userId: 'me',
          q: 'is:unread',
          maxResults,
        });

        const messages = response.data.messages || [];
        const emailPromises = messages.map(msg => this.getEmailDetails(msg.id!));

        return await Promise.all(emailPromises);
      } catch (error) {
        throw new Error(`获取未读邮件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 根据查询条件获取邮件列表
     */
    async searchEmails(query: string, maxResults = 10): Promise<EmailMessage[]> {
      try {
        this.ensureAuthenticated();
        const response = await this.gmail.users.messages.list({
          userId: 'me',
          q: query,
          maxResults,
        });

        const messages = response.data.messages || [];
        const emailPromises = messages.map(msg => this.getEmailDetails(msg.id!));

        return await Promise.all(emailPromises);
      } catch (error) {
        throw new Error(`搜索邮件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 获取邮件详细信息
     */
    async getEmailDetails(messageId: string): Promise<EmailMessage> {
      try {
        this.ensureAuthenticated();
        const response = await this.gmail.users.messages.get({
          userId: 'me',
          id: messageId,
          format: 'full',
        });

        const message = response.data;
        const headers = message.payload?.headers || [];

        // 提取邮件头信息
        const getHeader = (name: string): string => {
          const header = headers.find(h => h.name?.toLowerCase() === name.toLowerCase());
          return header?.value || '';
        };

        // 提取邮件正文
        const body = this.extractEmailBody(message.payload);

        // 检查是否未读
        const isUnread = message.labelIds?.includes('UNREAD') || false;

        return {
          id: message.id!,
          threadId: message.threadId!,
          subject: getHeader('Subject'),
          from: getHeader('From'),
          to: getHeader('To'),
          date: getHeader('Date'),
          body: body,
          snippet: message.snippet || '',
          isUnread,
        };
      } catch (error) {
        throw new Error(`获取邮件详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 发送邮件
     */
    async sendEmail(options: SendEmailOptions): Promise<string> {
      try {
        this.ensureAuthenticated();
        const email = this.createEmailMessage(options);

        const response = await this.gmail.users.messages.send({
          userId: 'me',
          requestBody: {
            raw: email,
            threadId: options.threadId,
          },
        });

        return response.data.id!;
      } catch (error) {
        throw new Error(`发送邮件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 回复邮件
     */
    async replyToEmail(originalMessageId: string, replyText: string, replyHtml?: string): Promise<string> {
      try {
        // 获取原始邮件信息
        const originalEmail = await this.getEmailDetails(originalMessageId);

        // 构造回复主题
        let replySubject = originalEmail.subject;
        if (!replySubject.toLowerCase().startsWith('re:')) {
          replySubject = `Re: ${replySubject}`;
        }

        // 发送回复
        return await this.sendEmail({
          to: originalEmail.from,
          subject: replySubject,
          text: replyText,
          html: replyHtml,
          replyToMessageId: originalMessageId,
          threadId: originalEmail.threadId,
        });
      } catch (error) {
        throw new Error(`回复邮件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 标记邮件为已读
     */
    async markAsRead(messageId: string): Promise<void> {
      try {
        await this.gmail.users.messages.modify({
          userId: 'me',
          id: messageId,
          requestBody: {
            removeLabelIds: ['UNREAD'],
          },
        });
      } catch (error) {
        throw new Error(`标记已读失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 批量标记邮件为已读
     */
    async markMultipleAsRead(messageIds: string[]): Promise<void> {
      try {
        const promises = messageIds.map(id => this.markAsRead(id));
        await Promise.all(promises);
      } catch (error) {
        throw new Error(`批量标记已读失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 获取邮件附件列表
     */
    async getEmailAttachments(messageId: string): Promise<AttachmentInfo[]> {
      try {
        const response = await this.gmail.users.messages.get({
          userId: 'me',
          id: messageId,
          format: 'full',
        });

        const attachments: AttachmentInfo[] = [];

        const extractAttachments = (part: gmail_v1.Schema$MessagePart) => {
          if (part.filename && part.body?.attachmentId) {
            attachments.push({
              attachmentId: part.body.attachmentId,
              filename: part.filename,
              mimeType: part.mimeType || '',
              size: part.body.size || 0,
            });
          }

          if (part.parts) {
            part.parts.forEach(extractAttachments);
          }
        };

        if (response.data.payload) {
          extractAttachments(response.data.payload);
        }

        return attachments;
      } catch (error) {
        throw new Error(`获取附件列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    /**
     * 下载邮件附件
     */
    async downloadAttachment(messageId: string, attachmentId: string): Promise<Buffer> {
      try {
        const response = await this.gmail.users.messages.attachments.get({
          userId: 'me',
          messageId: messageId,
          id: attachmentId,
        });

        const data = response.data.data;
        if (!data) {
          throw new Error('附件数据为空');
        }

        // Base64 解码
        return Buffer.from(data.replace(/-/g, '+').replace(/_/g, '/'), 'base64');
      } catch (error) {
        throw new Error(`下载附件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    // 私有辅助方法

    /**
     * 提取邮件正文内容
     */
    private extractEmailBody(payload?: gmail_v1.Schema$MessagePart): string {
      if (!payload) return '';

      let body = '';

      // 如果有直接的正文数据
      if (payload.body?.data) {
        body = Buffer.from(payload.body.data, 'base64').toString('utf-8');
      }
      // 如果是多部分邮件
      else if (payload.parts) {
        for (const part of payload.parts) {
          if (part.mimeType === 'text/plain' || part.mimeType === 'text/html') {
            if (part.body?.data) {
              body += Buffer.from(part.body.data, 'base64').toString('utf-8');
            }
          } else if (part.parts) {
            // 递归处理嵌套部分
            body += this.extractEmailBody(part);
          }
        }
      }

      return body;
    }

    /**
     * 创建邮件消息（RFC 2822 格式）
     */
    private createEmailMessage(options: SendEmailOptions): string {
      const boundary = `boundary_${Date.now()}`;
      let email = '';

      // 邮件头
      email += `To: ${options.to}\r\n`;
      email += `Subject: ${options.subject}\r\n`;

      if (options.replyToMessageId) {
        email += `In-Reply-To: <${options.replyToMessageId}>\r\n`;
        email += `References: <${options.replyToMessageId}>\r\n`;
      }

      // 如果同时有文本和HTML内容
      if (options.text && options.html) {
        email += `Content-Type: multipart/alternative; boundary="${boundary}"\r\n`;
        email += `MIME-Version: 1.0\r\n\r\n`;

        // 文本部分
        email += `--${boundary}\r\n`;
        email += `Content-Type: text/plain; charset=UTF-8\r\n\r\n`;
        email += `${options.text}\r\n\r\n`;

        // HTML部分
        email += `--${boundary}\r\n`;
        email += `Content-Type: text/html; charset=UTF-8\r\n\r\n`;
        email += `${options.html}\r\n\r\n`;

        email += `--${boundary}--\r\n`;
      }
      // 仅HTML内容
      else if (options.html) {
        email += `Content-Type: text/html; charset=UTF-8\r\n`;
        email += `MIME-Version: 1.0\r\n\r\n`;
        email += options.html;
      }
      // 仅文本内容
      else {
        email += `Content-Type: text/plain; charset=UTF-8\r\n`;
        email += `MIME-Version: 1.0\r\n\r\n`;
        email += options.text || '';
      }

      // Base64 编码
      return Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    }
}

/**
 * 创建 Gmail 服务实例
 */
export function createGmailService(): GmailService {
  return new GmailService();
}

/**
 * 获取授权URL的便捷函数
 */
export function getGmailAuthUrl(scopes?: string[]): string {
  const service = new GmailService();
  return service.getAuthUrl(scopes);
}

// 常用的 Gmail API 权限范围
export const GMAIL_SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.modify',
  'https://www.googleapis.com/auth/gmail.full',
];

// 导出默认实例创建函数
export default createGmailService;